import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/admin/payments/[id] - Get a specific payment
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)
  const { id } = await params

  const payment = await prisma.payments.findUnique({
    where: { id: BigInt(id) },
    include: {
      invoices: {
        include: {
          clients: {
            select: {
              id: true,
              companyname: true,
              contactname: true,
              contactemail: true
            }
          },
          project: {
            select: {
              id: true,
              name: true,
              status: true
            }
          },
          items: true
        }
      }
    }
  })

  if (!payment) {
    throw new ApiError('Payment not found', 404)
  }

  // Transform the response
  const transformedPayment = {
    ...transformFromDbFields(payment),
    invoice: payment.invoices ? {
      ...transformFromDbFields(payment.invoices),
      client: payment.invoices.clients ? transformFromDbFields(payment.invoices.clients) : null,
      project: payment.invoices.project ? transformFromDbFields(payment.invoices.project) : null,
      items: payment.invoices.items ? payment.invoices.items.map(item => transformFromDbFields(item)) : []
    } : null
  }

  return successResponse(transformedPayment)
})

// PUT /api/admin/payments/[id] - Update a payment
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)
  const { id } = await params

  const body = await request.json()
  console.log('Received payment update data:', JSON.stringify(body, null, 2))

  // Validate payment data
  const validatedData = schemas.payment.update.parse(body)
  console.log('Validated payment update data:', JSON.stringify(validatedData, null, 2))

  // Check if payment exists
  const existingPayment = await prisma.payments.findUnique({
    where: { id: BigInt(id) },
    include: {
      invoices: {
        include: {
          payments: {
            select: {
              id: true,
              amount: true,
              status: true
            }
          }
        }
      }
    }
  })

  if (!existingPayment) {
    throw new ApiError('Payment not found', 404)
  }

  // If amount is being updated, validate against invoice total
  if (validatedData.amount !== undefined) {
    const invoice = existingPayment.invoices
    const otherPayments = invoice.payments.filter(p => p.id !== BigInt(id) && p.status === 'COMPLETED')
    const otherPaidAmount = otherPayments.reduce((sum, p) => sum + Number(p.amount), 0)
    const newTotalPaid = otherPaidAmount + validatedData.amount

    if (newTotalPaid > Number(invoice.totalamount)) {
      throw new ApiError(
        `Updated payment amount would exceed invoice total. Invoice total: ${invoice.totalamount}, Other payments: ${otherPaidAmount}, Attempted payment: ${validatedData.amount}`,
        400
      )
    }
  }

  // Transform data for database
  const dbData = transformToDbFields(validatedData)

  // Update payment in a transaction
  const payment = await prisma.$transaction(async (tx) => {
    // Update the payment
    const updatedPayment = await tx.payments.update({
      where: { id: BigInt(id) },
      data: dbData,
      include: {
        invoices: {
          include: {
            clients: {
              select: {
                id: true,
                companyname: true,
                contactname: true,
                contactemail: true
              }
            },
            payments: {
              select: {
                amount: true,
                status: true
              }
            }
          }
        }
      }
    })

    // Recalculate invoice status
    const invoice = updatedPayment.invoices
    const totalPaid = invoice.payments
      .filter(p => p.status === 'COMPLETED')
      .reduce((sum, p) => sum + Number(p.amount), 0)

    const newStatus = totalPaid >= Number(invoice.totalamount) ? 'PAID' : 
                     totalPaid > 0 ? 'PARTIALLY_PAID' : 'SENT'

    await tx.invoices.update({
      where: { id: invoice.id },
      data: { 
        status: newStatus,
        paidat: newStatus === 'PAID' ? new Date() : null
      }
    })

    return updatedPayment
  })

  // Transform the response
  const transformedPayment = {
    ...transformFromDbFields(payment),
    invoice: payment.invoices ? {
      ...transformFromDbFields(payment.invoices),
      client: payment.invoices.clients ? transformFromDbFields(payment.invoices.clients) : null
    } : null
  }

  return successResponse(transformedPayment, 'Payment updated successfully')
})

// DELETE /api/admin/payments/[id] - Delete a payment
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)
  const { id } = await params

  // Get payment with invoice info
  const payment = await prisma.payments.findUnique({
    where: { id: BigInt(id) },
    include: {
      invoices: {
        include: {
          payments: {
            select: {
              id: true,
              amount: true,
              status: true
            }
          }
        }
      }
    }
  })

  if (!payment) {
    throw new ApiError('Payment not found', 404)
  }

  // Delete payment in a transaction
  await prisma.$transaction(async (tx) => {
    // Delete the payment
    await tx.payments.delete({
      where: { id: BigInt(id) }
    })

    // Recalculate invoice status
    const invoice = payment.invoices
    const remainingPayments = invoice.payments.filter(p => p.id !== BigInt(id) && p.status === 'COMPLETED')
    const totalPaid = remainingPayments.reduce((sum, p) => sum + Number(p.amount), 0)

    const newStatus = totalPaid >= Number(invoice.totalamount) ? 'PAID' : 
                     totalPaid > 0 ? 'PARTIALLY_PAID' : 'SENT'

    await tx.invoices.update({
      where: { id: invoice.id },
      data: { 
        status: newStatus,
        paidat: newStatus === 'PAID' ? new Date() : null
      }
    })
  })

  return successResponse(null, 'Payment deleted successfully')
})
