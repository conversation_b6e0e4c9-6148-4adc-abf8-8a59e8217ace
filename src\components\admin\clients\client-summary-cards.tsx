'use client'

import React from 'react'
import {
  BuildingOfficeIcon,
  UserGroupIcon,
  FolderIcon,
  DocumentTextIcon,
  CreditCardIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  CalendarIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline'

interface ClientStats {
  totalClients: number
  activeClients: number
  inactiveClients: number
  newClientsThisMonth: number
  totalProjects: number
  activeProjects: number
  totalContracts: number
  activeContracts: number
  totalContractValue: number
  totalInvoices: number
  paidInvoices: number
  pendingInvoices: number
  overdueInvoices: number
  totalRevenue: number
  monthlyGrowth: number
  averageProjectValue: number
  clientRetentionRate: number
}

interface ClientSummaryCardsProps {
  stats: ClientStats
  loading?: boolean
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

const formatPercentage = (value: number) => {
  return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`
}

export default function ClientSummaryCards({ stats, loading = false }: ClientSummaryCardsProps) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {[...Array(8)].map((_, index) => (
          <div key={index} className="animate-pulse">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="h-8 w-8 bg-gray-200 rounded"></div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-6 bg-gray-200 rounded"></div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  const cards = [
    {
      title: 'Total Clients',
      value: stats.totalClients.toLocaleString(),
      icon: BuildingOfficeIcon,
      color: 'blue',
      subtitle: `${stats.activeClients} active, ${stats.inactiveClients} inactive`,
      trend: stats.newClientsThisMonth > 0 ? {
        value: `+${stats.newClientsThisMonth}`,
        label: 'this month',
        positive: true
      } : undefined
    },
    {
      title: 'Active Projects',
      value: stats.activeProjects.toLocaleString(),
      icon: FolderIcon,
      color: 'green',
      subtitle: `${stats.totalProjects} total projects`,
      trend: {
        value: formatCurrency(stats.averageProjectValue),
        label: 'avg. value',
        positive: true
      }
    },
    {
      title: 'Active Contracts',
      value: stats.activeContracts.toLocaleString(),
      icon: DocumentTextIcon,
      color: 'purple',
      subtitle: `${stats.totalContracts} total contracts`,
      trend: {
        value: formatCurrency(stats.totalContractValue),
        label: 'total value',
        positive: true
      }
    },
    {
      title: 'Invoice Status',
      value: stats.totalInvoices.toLocaleString(),
      icon: CreditCardIcon,
      color: 'orange',
      subtitle: `${stats.paidInvoices} paid, ${stats.pendingInvoices} pending`,
      trend: stats.overdueInvoices > 0 ? {
        value: stats.overdueInvoices.toString(),
        label: 'overdue',
        positive: false
      } : undefined
    },
    {
      title: 'Total Revenue',
      value: formatCurrency(stats.totalRevenue),
      icon: CurrencyDollarIcon,
      color: 'emerald',
      subtitle: 'All-time revenue',
      trend: {
        value: formatPercentage(stats.monthlyGrowth),
        label: 'vs last month',
        positive: stats.monthlyGrowth >= 0
      }
    },
    {
      title: 'Client Retention',
      value: `${stats.clientRetentionRate.toFixed(1)}%`,
      icon: UserGroupIcon,
      color: 'indigo',
      subtitle: 'Client retention rate',
      trend: {
        value: stats.clientRetentionRate >= 90 ? 'Excellent' : stats.clientRetentionRate >= 80 ? 'Good' : 'Needs Improvement',
        label: 'performance',
        positive: stats.clientRetentionRate >= 80
      }
    },
    {
      title: 'New This Month',
      value: stats.newClientsThisMonth.toString(),
      icon: CalendarIcon,
      color: 'pink',
      subtitle: 'New clients acquired',
      trend: stats.newClientsThisMonth > 0 ? {
        value: 'Growing',
        label: 'client base',
        positive: true
      } : {
        value: 'Stable',
        label: 'client base',
        positive: true
      }
    },
    {
      title: 'Avg Project Value',
      value: formatCurrency(stats.averageProjectValue),
      icon: TrendingUpIcon,
      color: 'cyan',
      subtitle: 'Average project value',
      trend: {
        value: stats.totalProjects > 0 ? 'Active' : 'No Projects',
        label: 'portfolio',
        positive: stats.totalProjects > 0
      }
    }
  ]

  const getColorClasses = (color: string) => {
    const colors = {
      blue: 'bg-blue-500 text-white',
      green: 'bg-green-500 text-white',
      purple: 'bg-purple-500 text-white',
      orange: 'bg-orange-500 text-white',
      emerald: 'bg-emerald-500 text-white',
      indigo: 'bg-indigo-500 text-white',
      pink: 'bg-pink-500 text-white',
      cyan: 'bg-cyan-500 text-white'
    }
    return colors[color as keyof typeof colors] || colors.blue
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {cards.map((card, index) => {
        const Icon = card.icon
        return (
          <div
            key={index}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200"
          >
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className={`p-2 rounded-lg ${getColorClasses(card.color)}`}>
                  <Icon className="h-5 w-5" />
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">
                    {card.title}
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900">
                      {card.value}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
            
            <div className="mt-4">
              <div className="text-sm text-gray-600">
                {card.subtitle}
              </div>
              {card.trend && (
                <div className="flex items-center mt-2">
                  <div className={`flex items-center text-sm ${
                    card.trend.positive ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {card.trend.positive ? (
                      <TrendingUpIcon className="h-4 w-4 mr-1" />
                    ) : (
                      <TrendingDownIcon className="h-4 w-4 mr-1" />
                    )}
                    <span className="font-medium">{card.trend.value}</span>
                  </div>
                  <span className="text-sm text-gray-500 ml-2">
                    {card.trend.label}
                  </span>
                </div>
              )}
            </div>
          </div>
        )
      })}
    </div>
  )
}
