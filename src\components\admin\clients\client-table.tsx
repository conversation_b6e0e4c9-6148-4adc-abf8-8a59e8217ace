'use client'

import React, { useState } from 'react'
import {
  EyeIcon,
  PencilIcon,
  TrashIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  BuildingOfficeIcon,
  FolderIcon,
  DocumentTextIcon,
  CreditCardIcon,
  StarIcon,
  PhoneIcon,
  EnvelopeIcon,
  GlobeAltIcon,
  MapPinIcon
} from '@heroicons/react/24/outline'

interface Client {
  id: string
  companyName: string
  contactName: string
  contactEmail: string
  contactPhone?: string
  website?: string
  address?: string
  city?: string
  state?: string
  country?: string
  isActive: boolean
  createdAt: string
  _count: {
    projects: number
    contracts: number
    invoices: number
    orders: number
    testimonials: number
  }
  recentProjects?: Array<{
    id: string
    name: string
    status: string
    createdAt: string
  }>
  recentContracts?: Array<{
    id: string
    name: string
    status: string
    value: number
    createdAt: string
  }>
  recentInvoices?: Array<{
    id: string
    invoiceNumber: string
    totalAmount: number
    status: string
    createdAt: string
  }>
  recentOrders?: Array<{
    id: string
    title: string
    totalAmount: number
    status: string
    createdAt: string
  }>
}

interface ClientTableProps {
  clients: Client[]
  loading?: boolean
  onView: (client: Client) => void
  onEdit: (client: Client) => void
  onDelete: (id: string) => void
  onViewProjects: (client: Client) => void
  onViewContracts: (client: Client) => void
  onViewInvoices: (client: Client) => void
  onViewPayments: (client: Client) => void
}

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'active':
    case 'completed':
    case 'paid':
      return 'bg-green-100 text-green-800'
    case 'inactive':
    case 'cancelled':
      return 'bg-red-100 text-red-800'
    case 'pending':
    case 'draft':
      return 'bg-yellow-100 text-yellow-800'
    case 'in_progress':
    case 'sent':
      return 'bg-blue-100 text-blue-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

export default function ClientTable({
  clients,
  loading = false,
  onView,
  onEdit,
  onDelete,
  onViewProjects,
  onViewContracts,
  onViewInvoices,
  onViewPayments
}: ClientTableProps) {
  const [sortField, setSortField] = useState<string>('')
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc')
  const [selectedClients, setSelectedClients] = useState<string[]>([])
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set())

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedClients(clients.map(client => client.id))
    } else {
      setSelectedClients([])
    }
  }

  const handleSelectClient = (clientId: string, checked: boolean) => {
    if (checked) {
      setSelectedClients([...selectedClients, clientId])
    } else {
      setSelectedClients(selectedClients.filter(id => id !== clientId))
    }
  }

  const toggleRowExpansion = (clientId: string) => {
    const newExpanded = new Set(expandedRows)
    if (newExpanded.has(clientId)) {
      newExpanded.delete(clientId)
    } else {
      newExpanded.add(clientId)
    }
    setExpandedRows(newExpanded)
  }

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-10 bg-gray-200 rounded mb-4"></div>
        {[...Array(5)].map((_, index) => (
          <div key={index} className="h-16 bg-gray-100 rounded mb-2"></div>
        ))}
      </div>
    )
  }

  if (clients.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
          <BuildingOfficeIcon className="w-12 h-12 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No clients found</h3>
        <p className="text-gray-500">Get started by adding your first client.</p>
      </div>
    )
  }

  return (
    <div className="overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-3 py-3 text-left w-8">
                {/* Expand/Collapse column */}
              </th>
              <th className="px-6 py-3 text-left">
                <input
                  type="checkbox"
                  checked={selectedClients.length === clients.length}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('companyName')}
              >
                <div className="flex items-center space-x-1">
                  <span>Company</span>
                  {sortField === 'companyName' && (
                    sortDirection === 'asc' ? <ChevronUpIcon className="h-4 w-4" /> : <ChevronDownIcon className="h-4 w-4" />
                  )}
                </div>
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('contactName')}
              >
                <div className="flex items-center space-x-1">
                  <span>Contact</span>
                  {sortField === 'contactName' && (
                    sortDirection === 'asc' ? <ChevronUpIcon className="h-4 w-4" /> : <ChevronDownIcon className="h-4 w-4" />
                  )}
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Projects
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Contracts
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Invoices
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('isActive')}
              >
                <div className="flex items-center space-x-1">
                  <span>Status</span>
                  {sortField === 'isActive' && (
                    sortDirection === 'asc' ? <ChevronUpIcon className="h-4 w-4" /> : <ChevronDownIcon className="h-4 w-4" />
                  )}
                </div>
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('createdAt')}
              >
                <div className="flex items-center space-x-1">
                  <span>Created</span>
                  {sortField === 'createdAt' && (
                    sortDirection === 'asc' ? <ChevronUpIcon className="h-4 w-4" /> : <ChevronDownIcon className="h-4 w-4" />
                  )}
                </div>
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {clients.map((client) => {
              const isSelected = selectedClients.includes(client.id)
              const isExpanded = expandedRows.has(client.id)

              return (
                <React.Fragment key={client.id}>
                  <tr
                    className={`hover:bg-gray-50 ${isSelected ? 'bg-blue-50' : ''}`}
                  >
                    <td className="px-3 py-4 whitespace-nowrap">
                      <button
                        onClick={() => toggleRowExpansion(client.id)}
                        className="text-gray-400 hover:text-gray-600 transition-colors"
                      >
                        {isExpanded ? (
                          <ChevronUpIcon className="h-4 w-4" />
                        ) : (
                          <ChevronDownIcon className="h-4 w-4" />
                        )}
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={(e) => handleSelectClient(client.id, e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                            <BuildingOfficeIcon className="h-5 w-5 text-gray-500" />
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            {client.companyName}
                          </div>
                          {client.website && (
                            <div className="text-sm text-gray-500 flex items-center">
                              <GlobeAltIcon className="h-3 w-3 mr-1" />
                              {client.website}
                            </div>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">
                        {client.contactName}
                      </div>
                      <div className="text-sm text-gray-500 flex items-center">
                        <EnvelopeIcon className="h-3 w-3 mr-1" />
                        {client.contactEmail}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={() => onViewProjects(client)}
                        className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
                      >
                        <FolderIcon className="h-4 w-4 mr-1" />
                        {client._count.projects}
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={() => onViewContracts(client)}
                        className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
                      >
                        <DocumentTextIcon className="h-4 w-4 mr-1" />
                        {client._count.contracts}
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={() => onViewInvoices(client)}
                        className="text-sm text-blue-600 hover:text-blue-800 flex items-center"
                      >
                        <CreditCardIcon className="h-4 w-4 mr-1" />
                        {client._count.invoices}
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        client.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {client.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(client.createdAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center space-x-1">
                        <button
                          onClick={() => onView(client)}
                          className="text-gray-400 hover:text-blue-600 transition-colors p-1"
                          title="View Details"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => onEdit(client)}
                          className="text-gray-400 hover:text-blue-600 transition-colors p-1"
                          title="Edit"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => onViewProjects(client)}
                          className="text-gray-400 hover:text-green-600 transition-colors p-1"
                          title="View Projects"
                        >
                          <FolderIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => onViewContracts(client)}
                          className="text-gray-400 hover:text-purple-600 transition-colors p-1"
                          title="View Contracts"
                        >
                          <DocumentTextIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => onViewInvoices(client)}
                          className="text-gray-400 hover:text-orange-600 transition-colors p-1"
                          title="View Invoices"
                        >
                          <CreditCardIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => onDelete(client.id)}
                          className="text-gray-400 hover:text-red-600 transition-colors p-1"
                          title="Delete"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>

                  {/* Expandable content row */}
                  {isExpanded && (
                    <tr className="bg-gray-50">
                      <td colSpan={10} className="px-6 py-4">
                        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                          {/* Contact Information */}
                          <div>
                            <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                              <EnvelopeIcon className="h-4 w-4 mr-2" />
                              Contact Details
                            </h4>
                            <div className="space-y-2">
                              <div className="text-sm">
                                <span className="font-medium">Email:</span> {client.contactEmail}
                              </div>
                              {client.contactPhone && (
                                <div className="text-sm">
                                  <span className="font-medium">Phone:</span> {client.contactPhone}
                                </div>
                              )}
                              {client.address && (
                                <div className="text-sm">
                                  <span className="font-medium">Address:</span>
                                  <div className="text-gray-600">
                                    {client.address}
                                    {client.city && `, ${client.city}`}
                                    {client.state && `, ${client.state}`}
                                    {client.country && `, ${client.country}`}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Recent Projects */}
                          <div>
                            <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                              <FolderIcon className="h-4 w-4 mr-2" />
                              Recent Projects ({client._count.projects})
                            </h4>
                            <div className="space-y-2">
                              {client.recentProjects && client.recentProjects.length > 0 ? (
                                client.recentProjects.map((project) => (
                                  <div key={project.id} className="bg-white rounded-lg p-3 border">
                                    <div className="flex justify-between items-start">
                                      <div className="flex-1">
                                        <div className="font-medium text-sm">{project.name}</div>
                                        <div className="text-xs text-gray-500">
                                          {formatDate(project.createdAt)}
                                        </div>
                                      </div>
                                      <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(project.status)}`}>
                                        {project.status}
                                      </span>
                                    </div>
                                  </div>
                                ))
                              ) : (
                                <div className="text-sm text-gray-500 italic">No projects yet</div>
                              )}
                            </div>
                          </div>

                          {/* Recent Contracts */}
                          <div>
                            <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                              <DocumentTextIcon className="h-4 w-4 mr-2" />
                              Recent Contracts ({client._count.contracts})
                            </h4>
                            <div className="space-y-2">
                              {client.recentContracts && client.recentContracts.length > 0 ? (
                                client.recentContracts.map((contract) => (
                                  <div key={contract.id} className="bg-white rounded-lg p-3 border">
                                    <div className="flex justify-between items-start">
                                      <div className="flex-1">
                                        <div className="font-medium text-sm">{contract.name}</div>
                                        <div className="text-xs text-gray-500">
                                          {formatCurrency(contract.value)}
                                        </div>
                                      </div>
                                      <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(contract.status)}`}>
                                        {contract.status}
                                      </span>
                                    </div>
                                  </div>
                                ))
                              ) : (
                                <div className="text-sm text-gray-500 italic">No contracts yet</div>
                              )}
                            </div>
                          </div>

                          {/* Recent Invoices */}
                          <div>
                            <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                              <CreditCardIcon className="h-4 w-4 mr-2" />
                              Recent Invoices ({client._count.invoices})
                            </h4>
                            <div className="space-y-2">
                              {client.recentInvoices && client.recentInvoices.length > 0 ? (
                                client.recentInvoices.map((invoice) => (
                                  <div key={invoice.id} className="bg-white rounded-lg p-3 border">
                                    <div className="flex justify-between items-start">
                                      <div className="flex-1">
                                        <div className="font-medium text-sm">{invoice.invoiceNumber}</div>
                                        <div className="text-xs text-gray-500">
                                          {formatCurrency(invoice.totalAmount)}
                                        </div>
                                      </div>
                                      <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(invoice.status)}`}>
                                        {invoice.status}
                                      </span>
                                    </div>
                                  </div>
                                ))
                              ) : (
                                <div className="text-sm text-gray-500 italic">No invoices yet</div>
                              )}
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  )}
                </React.Fragment>
              )
            })}
          </tbody>
        </table>
      </div>
    </div>
  )
}
