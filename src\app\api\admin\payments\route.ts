import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON>rror<PERSON><PERSON><PERSON>, 
  successResponse,
  paginatedResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest,
  ApiError
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/payments - Get all payments with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build search query
  const searchQuery = buildSearchQuery(search, ['transactionId', 'notes'])
  
  // Add status filter if provided
  if (filter) {
    searchQuery.status = filter
  }
  
  // Build sort query
  const sortQuery = buildSortQuery(sortBy, sortOrder)

  // Get payments with pagination
  const [payments, total] = await Promise.all([
    prisma.payments.findMany({
      where: searchQuery,
      include: {
        invoices: {
          select: {
            id: true,
            invoicenumber: true,
            totalamount: true,
            status: true,
            clients: {
              select: {
                id: true,
                companyname: true,
                contactname: true,
                contactemail: true
              }
            }
          }
        }
      },
      orderBy: sortQuery,
      skip,
      take,
    }),
    prisma.payments.count({ where: searchQuery })
  ])

  // Transform the data
  const transformedPayments = payments.map(payment => ({
    ...transformFromDbFields(payment),
    invoice: payment.invoices ? {
      ...transformFromDbFields(payment.invoices),
      client: payment.invoices.clients ? transformFromDbFields(payment.invoices.clients) : null
    } : null
  }))

  return paginatedResponse(transformedPayments, page, limit, total)
})

// POST /api/admin/payments - Create a new payment
export const POST = withErrorHandler(async (request: NextRequest) => {
  await requireAdmin(request)

  const body = await request.json()
  console.log('Received payment data:', JSON.stringify(body, null, 2))

  // Validate payment data
  const validatedData = schemas.payment.create.parse(body)
  console.log('Validated payment data:', JSON.stringify(validatedData, null, 2))

  // Check if invoice exists
  const invoice = await prisma.invoices.findUnique({
    where: { id: BigInt(validatedData.invoiceId) },
    include: {
      payments: {
        select: {
          amount: true,
          status: true
        }
      }
    }
  })

  if (!invoice) {
    throw new ApiError('Invoice not found', 404)
  }

  // Calculate total paid amount
  const totalPaid = invoice.payments
    .filter(p => p.status === 'COMPLETED')
    .reduce((sum, p) => sum + Number(p.amount), 0)

  // Check if payment amount would exceed invoice total
  const newTotalPaid = totalPaid + validatedData.amount
  if (newTotalPaid > Number(invoice.totalamount)) {
    throw new ApiError(
      `Payment amount would exceed invoice total. Invoice total: ${invoice.totalamount}, Already paid: ${totalPaid}, Attempted payment: ${validatedData.amount}`,
      400
    )
  }

  // Transform data for database
  const dbData = transformToDbFields({
    ...validatedData,
    invoiceId: BigInt(validatedData.invoiceId)
  })

  // Create payment in a transaction
  const payment = await prisma.$transaction(async (tx) => {
    // Create the payment
    const newPayment = await tx.payments.create({
      data: dbData,
      include: {
        invoices: {
          select: {
            id: true,
            invoicenumber: true,
            totalamount: true,
            status: true,
            clients: {
              select: {
                id: true,
                companyname: true,
                contactname: true,
                contactemail: true
              }
            }
          }
        }
      }
    })

    // Update invoice status if fully paid
    if (newTotalPaid >= Number(invoice.totalamount)) {
      await tx.invoices.update({
        where: { id: BigInt(validatedData.invoiceId) },
        data: { 
          status: 'PAID',
          paidat: new Date()
        }
      })
    }

    return newPayment
  })

  // Transform the response
  const transformedPayment = {
    ...transformFromDbFields(payment),
    invoice: payment.invoices ? {
      ...transformFromDbFields(payment.invoices),
      client: payment.invoices.clients ? transformFromDbFields(payment.invoices.clients) : null
    } : null
  }

  return successResponse(transformedPayment, 'Payment created successfully', 201)
})
