import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON>rror<PERSON>and<PERSON>, 
  successResponse,
  paginatedResponse,
  requireAdmin,
  getQueryParams,
  getPaginationParams,
  buildSearchQuery,
  buildSortQuery,
  validateRequest
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

// GET /api/admin/invoices - Get all invoices with pagination and search
export const GET = withErrorHandler(async (request: NextRequest) => {
  // Temporarily bypass auth for development/testing
  // await requireAdmin(request)

  const { page, limit, search, sortBy, sortOrder, filter } = getQueryParams(request)
  const { skip, take } = getPaginationParams(page, limit)

  // Build search query
  const searchQuery = buildSearchQuery(search, ['invoicenumber', 'description'])
  
  // Add status filter if provided
  if (filter) {
    searchQuery.status = filter
  }
  
  // Build sort query
  const sortQuery = buildSortQuery(sortBy, sortOrder)

  // Get invoices with pagination
  const [invoices, total] = await Promise.all([
    prisma.invoices.findMany({
      where: searchQuery,
      include: {
        clients: {
          select: {
            id: true,
            companyname: true,
            contactname: true,
            contactemail: true
          }
        },
        projects: {
          select: {
            id: true,
            name: true,
            status: true
          }
        },
        orders: {
          select: {
            id: true,
            ordernumber: true,
            status: true
          }
        },
        contracts: {
          select: {
            id: true,
            title: true,
            status: true
          }
        },
        invoiceitems: true,
        payments: {
          select: {
            id: true,
            amount: true,
            status: true,
            paymentdate: true
          }
        }
      },
      orderBy: sortQuery,
      skip,
      take,
    }),
    prisma.invoices.count({ where: searchQuery })
  ])

  // Transform the data to match expected format
  const transformedInvoices = invoices.map(invoice => ({
    ...transformFromDbFields(invoice),
    client: invoice.clients ? transformFromDbFields(invoice.clients) : null,
    project: invoice.projects ? transformFromDbFields(invoice.projects) : null,
    order: invoice.orders ? transformFromDbFields(invoice.orders) : null,
    contract: invoice.contracts ? transformFromDbFields(invoice.contracts) : null,
    items: invoice.invoiceitems ? invoice.invoiceitems.map(item => transformFromDbFields(item)) : [],
    payments: invoice.payments ? invoice.payments.map(payment => ({
      ...transformFromDbFields(payment),
      paidAt: payment.paymentdate
    })) : []
  }))

  return paginatedResponse(transformedInvoices, page, limit, total)
})

// POST /api/admin/invoices - Create a new invoice
export const POST = withErrorHandler(async (request: NextRequest) => {
  // Temporarily bypass auth for development/testing
  // await requireAdmin(request)

  const body = await request.json()
  console.log('Received invoice data:', JSON.stringify(body, null, 2))

  const { items, ...invoiceData } = body

  // Validate invoice data
  console.log('Validating invoice data:', JSON.stringify(invoiceData, null, 2))
  const validatedData = schemas.invoice.create.parse(invoiceData)

  // Transform data for database
  const dbData = transformToDbFields(validatedData)

  // Create invoice with items in a transaction
  const invoice = await prisma.$transaction(async (tx) => {
    // Create the invoice
    const newInvoice = await tx.invoices.create({
      data: dbData,
    })

    // Create invoice items if provided
    if (items && Array.isArray(items) && items.length > 0) {
      const validItems = items.filter(item => item.description && item.description.trim() !== '')

      if (validItems.length > 0) {
        await tx.invoiceitems.createMany({
          data: validItems.map(item => ({
            invoiceid: newInvoice.id,
            description: item.description,
            quantity: Number(item.quantity) || 1,
            unitprice: Number(item.unitPrice) || 0,
            totalprice: Number(item.totalPrice) || 0,
          }))
        })
      }
    }

    // Return the complete invoice with relations
    return await tx.invoices.findUnique({
      where: { id: newInvoice.id },
      include: {
        clients: {
          select: {
            id: true,
            companyname: true,
            contactname: true,
            contactemail: true
          }
        },
        projects: {
          select: {
            id: true,
            name: true,
            status: true
          }
        },
        invoiceitems: true,
        payments: {
          select: {
            id: true,
            amount: true,
            status: true,
            paymentdate: true
          }
        }
      }
    })
  })

  // Transform the response
  const transformedInvoice = {
    ...transformFromDbFields(invoice),
    client: invoice.clients ? transformFromDbFields(invoice.clients) : null,
    project: invoice.projects ? transformFromDbFields(invoice.projects) : null,
    items: invoice.invoiceitems ? invoice.invoiceitems.map(item => transformFromDbFields(item)) : [],
    payments: invoice.payments ? invoice.payments.map(payment => ({
      ...transformFromDbFields(payment),
      paidAt: payment.paymentdate
    })) : []
  }

  return successResponse(transformedInvoice, 'Invoice created successfully', 201)
})
