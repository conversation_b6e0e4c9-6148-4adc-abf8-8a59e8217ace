'use client'

import React, { useState } from 'react'
import {
  EyeIcon,
  PencilIcon,
  TrashIcon,
  ChevronUpIcon,
  ChevronDownIcon,
  BuildingOfficeIcon,
  FolderIcon,
  DocumentTextIcon,
  CreditCardIcon,
  StarIcon,
  PhoneIcon,
  EnvelopeIcon,
  GlobeAltIcon,
  MapPinIcon,
  UserIcon,
  CalendarIcon
} from '@heroicons/react/24/outline'

interface Client {
  id: string
  companyName: string
  contactName: string
  contactEmail: string
  contactPhone?: string
  website?: string
  address?: string
  city?: string
  state?: string
  country?: string
  isActive: boolean
  createdAt: string
  _count: {
    projects: number
    contracts: number
    invoices: number
    orders: number
    testimonials: number
  }
  recentProjects?: Array<{
    id: string
    name: string
    status: string
    createdAt: string
  }>
  recentContracts?: Array<{
    id: string
    name: string
    status: string
    value: number
    createdAt: string
  }>
  recentInvoices?: Array<{
    id: string
    invoiceNumber: string
    totalAmount: number
    status: string
    createdAt: string
  }>
  recentOrders?: Array<{
    id: string
    title: string
    totalAmount: number
    status: string
    createdAt: string
  }>
}

interface ClientCardProps {
  client: Client
  onView: (client: Client) => void
  onEdit: (client: Client) => void
  onDelete: (id: string) => void
  onViewProjects: (client: Client) => void
  onViewContracts: (client: Client) => void
  onViewInvoices: (client: Client) => void
  onViewPayments: (client: Client) => void
}

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'active':
    case 'completed':
    case 'paid':
      return 'bg-green-100 text-green-800'
    case 'inactive':
    case 'cancelled':
      return 'bg-red-100 text-red-800'
    case 'pending':
    case 'draft':
      return 'bg-yellow-100 text-yellow-800'
    case 'in_progress':
    case 'sent':
      return 'bg-blue-100 text-blue-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

export default function ClientCard({
  client,
  onView,
  onEdit,
  onDelete,
  onViewProjects,
  onViewContracts,
  onViewInvoices,
  onViewPayments
}: ClientCardProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  const toggleExpansion = () => {
    setIsExpanded(!isExpanded)
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200">
      {/* Card Header */}
      <div className="p-6">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              <div className="h-12 w-12 rounded-full bg-gray-100 flex items-center justify-center">
                <BuildingOfficeIcon className="h-6 w-6 text-gray-500" />
              </div>
            </div>
            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-medium text-gray-900 truncate">
                {client.companyName}
              </h3>
              <div className="flex items-center text-sm text-gray-500 mt-1">
                <UserIcon className="h-4 w-4 mr-1" />
                {client.contactName}
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              client.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
            }`}>
              {client.isActive ? 'Active' : 'Inactive'}
            </span>
          </div>
        </div>

        {/* Contact Information */}
        <div className="mt-4 space-y-2">
          <div className="flex items-center text-sm text-gray-600">
            <EnvelopeIcon className="h-4 w-4 mr-2 text-gray-400" />
            <span className="truncate">{client.contactEmail}</span>
          </div>
          {client.contactPhone && (
            <div className="flex items-center text-sm text-gray-600">
              <PhoneIcon className="h-4 w-4 mr-2 text-gray-400" />
              <span>{client.contactPhone}</span>
            </div>
          )}
          {client.website && (
            <div className="flex items-center text-sm text-gray-600">
              <GlobeAltIcon className="h-4 w-4 mr-2 text-gray-400" />
              <span className="truncate">{client.website}</span>
            </div>
          )}
          {(client.address || client.city || client.state || client.country) && (
            <div className="flex items-center text-sm text-gray-600">
              <MapPinIcon className="h-4 w-4 mr-2 text-gray-400" />
              <span className="truncate">
                {[client.address, client.city, client.state, client.country]
                  .filter(Boolean)
                  .join(', ')}
              </span>
            </div>
          )}
        </div>

        {/* Statistics */}
        <div className="mt-6 grid grid-cols-4 gap-4">
          <button
            onClick={() => onViewProjects(client)}
            className="text-center p-3 rounded-lg bg-blue-50 hover:bg-blue-100 transition-colors"
          >
            <FolderIcon className="h-5 w-5 text-blue-600 mx-auto mb-1" />
            <div className="text-lg font-semibold text-blue-900">{client._count?.projects || 0}</div>
            <div className="text-xs text-blue-600">Projects</div>
          </button>
          <button
            onClick={() => onViewContracts(client)}
            className="text-center p-3 rounded-lg bg-green-50 hover:bg-green-100 transition-colors"
          >
            <DocumentTextIcon className="h-5 w-5 text-green-600 mx-auto mb-1" />
            <div className="text-lg font-semibold text-green-900">{client._count?.contracts || 0}</div>
            <div className="text-xs text-green-600">Contracts</div>
          </button>
          <button
            onClick={() => onViewInvoices(client)}
            className="text-center p-3 rounded-lg bg-purple-50 hover:bg-purple-100 transition-colors"
          >
            <CreditCardIcon className="h-5 w-5 text-purple-600 mx-auto mb-1" />
            <div className="text-lg font-semibold text-purple-900">{client._count?.invoices || 0}</div>
            <div className="text-xs text-purple-600">Invoices</div>
          </button>
          <button
            onClick={() => onViewPayments(client)}
            className="text-center p-3 rounded-lg bg-orange-50 hover:bg-orange-100 transition-colors"
          >
            <StarIcon className="h-5 w-5 text-orange-600 mx-auto mb-1" />
            <div className="text-lg font-semibold text-orange-900">{client._count?.testimonials || 0}</div>
            <div className="text-xs text-orange-600">Reviews</div>
          </button>
        </div>

        {/* Created Date */}
        <div className="mt-4 flex items-center text-sm text-gray-500">
          <CalendarIcon className="h-4 w-4 mr-2" />
          Created {formatDate(client.createdAt)}
        </div>
      </div>

      {/* Expandable Section */}
      {isExpanded && (
        <div className="border-t border-gray-200 p-6 bg-gray-50">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Recent Projects */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                <FolderIcon className="h-4 w-4 mr-2" />
                Recent Projects
              </h4>
              <div className="space-y-2">
                {client.recentProjects && client.recentProjects.length > 0 ? (
                  client.recentProjects.map((project) => (
                    <div key={project.id} className="bg-white rounded-lg p-3 border">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="font-medium text-sm">{project.name}</div>
                          <div className="text-xs text-gray-500">
                            {formatDate(project.createdAt)}
                          </div>
                        </div>
                        <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(project.status)}`}>
                          {project.status}
                        </span>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-sm text-gray-500 italic">No projects yet</div>
                )}
              </div>
            </div>

            {/* Recent Contracts */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                <DocumentTextIcon className="h-4 w-4 mr-2" />
                Recent Contracts
              </h4>
              <div className="space-y-2">
                {client.recentContracts && client.recentContracts.length > 0 ? (
                  client.recentContracts.map((contract) => (
                    <div key={contract.id} className="bg-white rounded-lg p-3 border">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="font-medium text-sm">{contract.name}</div>
                          <div className="text-xs text-gray-500">
                            {formatCurrency(contract.value)}
                          </div>
                        </div>
                        <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(contract.status)}`}>
                          {contract.status}
                        </span>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-sm text-gray-500 italic">No contracts yet</div>
                )}
              </div>
            </div>

            {/* Recent Invoices */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                <CreditCardIcon className="h-4 w-4 mr-2" />
                Recent Invoices
              </h4>
              <div className="space-y-2">
                {client.recentInvoices && client.recentInvoices.length > 0 ? (
                  client.recentInvoices.map((invoice) => (
                    <div key={invoice.id} className="bg-white rounded-lg p-3 border">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="font-medium text-sm">{invoice.invoiceNumber}</div>
                          <div className="text-xs text-gray-500">
                            {formatCurrency(invoice.totalAmount)}
                          </div>
                        </div>
                        <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(invoice.status)}`}>
                          {invoice.status}
                        </span>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-sm text-gray-500 italic">No invoices yet</div>
                )}
              </div>
            </div>

            {/* Recent Orders */}
            <div>
              <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                <StarIcon className="h-4 w-4 mr-2" />
                Recent Orders
              </h4>
              <div className="space-y-2">
                {client.recentOrders && client.recentOrders.length > 0 ? (
                  client.recentOrders.map((order) => (
                    <div key={order.id} className="bg-white rounded-lg p-3 border">
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <div className="font-medium text-sm">{order.title}</div>
                          <div className="text-xs text-gray-500">
                            {formatCurrency(order.totalAmount)}
                          </div>
                        </div>
                        <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(order.status)}`}>
                          {order.status}
                        </span>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-sm text-gray-500 italic">No orders yet</div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Card Footer */}
      <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between">
        <button
          onClick={toggleExpansion}
          className="flex items-center text-sm text-gray-600 hover:text-gray-900 transition-colors"
        >
          {isExpanded ? (
            <>
              <ChevronUpIcon className="h-4 w-4 mr-1" />
              Show Less
            </>
          ) : (
            <>
              <ChevronDownIcon className="h-4 w-4 mr-1" />
              Show More
            </>
          )}
        </button>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => onView(client)}
            className="p-2 text-gray-400 hover:text-blue-600 transition-colors rounded-lg hover:bg-white"
            title="View Details"
          >
            <EyeIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => onEdit(client)}
            className="p-2 text-gray-400 hover:text-blue-600 transition-colors rounded-lg hover:bg-white"
            title="Edit"
          >
            <PencilIcon className="h-4 w-4" />
          </button>
          <button
            onClick={() => onDelete(client.id)}
            className="p-2 text-gray-400 hover:text-red-600 transition-colors rounded-lg hover:bg-white"
            title="Delete"
          >
            <TrashIcon className="h-4 w-4" />
          </button>
        </div>
      </div>
    </div>
  )
}
