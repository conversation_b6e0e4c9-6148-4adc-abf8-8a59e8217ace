import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/admin/invoices/[id] - Get a specific invoice
export const GET = withError<PERSON><PERSON><PERSON>(async (request: NextRequest, { params }: RouteParams) => {
  // Temporarily bypass auth for development/testing
  // await requireAdmin(request)
  const { id } = await params

  const invoice = await prisma.invoices.findUnique({
    where: { id: BigInt(id) },
    include: {
      clients: true,
      projects: true,
      orders: true,
      contracts: true,
      invoiceitems: true,
      payments: true
    }
  })

  if (!invoice) {
    throw new ApiError('Invoice not found', 404)
  }

  // Transform the response
  const transformedInvoice = {
    id: Number(invoice.id),
    invoiceNumber: invoice.invoicenumber,
    dueDate: invoice.duedate,
    subtotal: Number(invoice.subtotal || 0),
    taxRate: Number(invoice.taxrate || 0),
    taxAmount: Number(invoice.taxamount || 0),
    totalAmount: Number(invoice.totalamount || 0),
    status: invoice.status,
    description: invoice.description,
    clientId: Number(invoice.clientid),
    contractId: invoice.contid ? Number(invoice.contid) : null,
    orderId: invoice.orderid ? Number(invoice.orderid) : null,
    projectId: invoice.projectid ? Number(invoice.projectid) : null,
    paidAt: invoice.paidat,
    createdAt: invoice.createdat,
    updatedAt: invoice.updatedat,
    client: invoice.clients ? {
      id: Number(invoice.clients.id),
      companyName: invoice.clients.companyname,
      contactName: invoice.clients.contactname,
      contactEmail: invoice.clients.contactemail
    } : null,
    project: invoice.projects ? {
      id: Number(invoice.projects.id),
      name: invoice.projects.name,
      status: invoice.projects.status
    } : null,
    order: invoice.orders ? {
      id: Number(invoice.orders.id),
      orderNumber: invoice.orders.ordernumber,
      status: invoice.orders.status
    } : null,
    contract: invoice.contracts ? {
      id: Number(invoice.contracts.id),
      title: invoice.contracts.title,
      status: invoice.contracts.contstatus
    } : null,
    items: invoice.invoiceitems ? invoice.invoiceitems.map(item => ({
      id: Number(item.id),
      description: item.description,
      quantity: Number(item.quantity),
      unitPrice: Number(item.unitprice),
      totalPrice: Number(item.totalprice)
    })) : [],
    payments: invoice.payments ? invoice.payments.map(payment => ({
      id: Number(payment.id),
      amount: Number(payment.amount),
      status: payment.status,
      paymentDate: payment.paymentdate
    })) : []
  }

  return successResponse(transformedInvoice)
})

// PUT /api/admin/invoices/[id] - Update an invoice
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  // Temporarily bypass auth for development/testing
  // await requireAdmin(request)
  const { id } = await params

  const body = await request.json()
  console.log('Received invoice update data:', JSON.stringify(body, null, 2))

  const { items, ...invoiceData } = body

  // Validate invoice data
  console.log('Validating invoice update data:', JSON.stringify(invoiceData, null, 2))
  const validatedData = schemas.invoice.update.parse(invoiceData)

  // Transform data for database
  const dbData = transformToDbFields.invoice(validatedData)

  // Update invoice with items in a transaction
  const invoice = await prisma.$transaction(async (tx) => {
    // Update the invoice
    const updatedInvoice = await tx.invoices.update({
      where: { id: BigInt(id) },
      data: dbData,
    })

    // Delete existing items and create new ones if provided
    await tx.invoiceitems.deleteMany({
      where: { invoiceid: BigInt(id) }
    })

    if (items && Array.isArray(items) && items.length > 0) {
      const validItems = items.filter(item => item.description && item.description.trim() !== '')

      if (validItems.length > 0) {
        await tx.invoiceitems.createMany({
          data: validItems.map(item => ({
            invoiceid: BigInt(id),
            description: item.description,
            quantity: Number(item.quantity) || 1,
            unitprice: Number(item.unitPrice) || 0,
            totalprice: Number(item.totalPrice) || 0,
          }))
        })
      }
    }

    // Return the complete updated invoice with relations
    return await tx.invoices.findUnique({
      where: { id: BigInt(id) },
      include: {
        clients: {
          select: {
            id: true,
            companyname: true,
            contactname: true,
            contactemail: true
          }
        },
        projects: {
          select: {
            id: true,
            name: true,
            status: true
          }
        },
        invoiceitems: true,
        payments: {
          select: {
            id: true,
            amount: true,
            status: true,
            paymentdate: true
          }
        }
      }
    })
  })

  // Transform the response
  const transformedInvoice = {
    id: Number(invoice.id),
    invoiceNumber: invoice.invoicenumber,
    dueDate: invoice.duedate,
    subtotal: Number(invoice.subtotal || 0),
    taxRate: Number(invoice.taxrate || 0),
    taxAmount: Number(invoice.taxamount || 0),
    totalAmount: Number(invoice.totalamount || 0),
    status: invoice.status,
    description: invoice.description,
    clientId: Number(invoice.clientid),
    contractId: invoice.contid ? Number(invoice.contid) : null,
    orderId: invoice.orderid ? Number(invoice.orderid) : null,
    projectId: invoice.projectid ? Number(invoice.projectid) : null,
    paidAt: invoice.paidat,
    createdAt: invoice.createdat,
    updatedAt: invoice.updatedat,
    client: invoice.clients ? {
      id: Number(invoice.clients.id),
      companyName: invoice.clients.companyname,
      contactName: invoice.clients.contactname,
      contactEmail: invoice.clients.contactemail
    } : null,
    project: invoice.projects ? {
      id: Number(invoice.projects.id),
      name: invoice.projects.name,
      status: invoice.projects.status
    } : null,
    items: invoice.invoiceitems ? invoice.invoiceitems.map(item => ({
      id: Number(item.id),
      description: item.description,
      quantity: Number(item.quantity),
      unitPrice: Number(item.unitprice),
      totalPrice: Number(item.totalprice)
    })) : [],
    payments: invoice.payments ? invoice.payments.map(payment => ({
      id: Number(payment.id),
      amount: Number(payment.amount),
      status: payment.status,
      paymentDate: payment.paymentdate,
      paidAt: payment.paymentdate
    })) : []
  }

  return successResponse(transformedInvoice, 'Invoice updated successfully')
})

// DELETE /api/admin/invoices/[id] - Delete an invoice
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  // Temporarily bypass auth for development/testing
  // await requireAdmin(request)
  const { id } = await params

  // Check if invoice has payments
  const paymentsCount = await prisma.payments.count({
    where: { invoiceid: BigInt(id) }
  })

  if (paymentsCount > 0) {
    throw new ApiError('Cannot delete invoice with associated payments', 400)
  }

  // Delete invoice items first
  await prisma.invoiceitems.deleteMany({
    where: { invoiceid: BigInt(id) }
  })

  // Then delete the invoice
  await prisma.invoices.delete({
    where: { id: BigInt(id) }
  })

  return successResponse(null, 'Invoice deleted successfully')
})
