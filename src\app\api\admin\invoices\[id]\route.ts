import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/admin/invoices/[id] - Get a specific invoice
export const GET = withError<PERSON><PERSON><PERSON>(async (request: NextRequest, { params }: RouteParams) => {
  // Temporarily bypass auth for development/testing
  // await requireAdmin(request)
  const { id } = await params

  const invoice = await prisma.invoices.findUnique({
    where: { id: BigInt(id) },
    include: {
      clients: true,
      projects: true,
      orders: true,
      contracts: true,
      invoiceitems: true,
      payments: true
    }
  })

  if (!invoice) {
    throw new ApiError('Invoice not found', 404)
  }

  // Transform the response
  const transformedInvoice = {
    ...transformFromDbFields(invoice),
    client: invoice.clients ? transformFromDbFields(invoice.clients) : null,
    project: invoice.projects ? transformFromDbFields(invoice.projects) : null,
    order: invoice.orders ? transformFromDbFields(invoice.orders) : null,
    contract: invoice.contracts ? transformFromDbFields(invoice.contracts) : null,
    items: invoice.invoiceitems ? invoice.invoiceitems.map(item => transformFromDbFields(item)) : [],
    payments: invoice.payments ? invoice.payments.map(payment => transformFromDbFields(payment)) : []
  }

  return successResponse(transformedInvoice)
})

// PUT /api/admin/invoices/[id] - Update an invoice
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  // Temporarily bypass auth for development/testing
  // await requireAdmin(request)
  const { id } = await params

  const body = await request.json()
  console.log('Received invoice update data:', JSON.stringify(body, null, 2))

  const { items, ...invoiceData } = body

  // Validate invoice data
  console.log('Validating invoice update data:', JSON.stringify(invoiceData, null, 2))
  const validatedData = schemas.invoice.update.parse(invoiceData)

  // Transform data for database
  const dbData = transformToDbFields(validatedData)

  // Update invoice with items in a transaction
  const invoice = await prisma.$transaction(async (tx) => {
    // Update the invoice
    const updatedInvoice = await tx.invoices.update({
      where: { id: BigInt(id) },
      data: dbData,
    })

    // Delete existing items and create new ones if provided
    await tx.invoiceitems.deleteMany({
      where: { invoiceid: BigInt(id) }
    })

    if (items && Array.isArray(items) && items.length > 0) {
      const validItems = items.filter(item => item.description && item.description.trim() !== '')

      if (validItems.length > 0) {
        await tx.invoiceitems.createMany({
          data: validItems.map(item => ({
            invoiceid: BigInt(id),
            description: item.description,
            quantity: Number(item.quantity) || 1,
            unitprice: Number(item.unitPrice) || 0,
            totalprice: Number(item.totalPrice) || 0,
          }))
        })
      }
    }

    // Return the complete updated invoice with relations
    return await tx.invoices.findUnique({
      where: { id: BigInt(id) },
      include: {
        clients: {
          select: {
            id: true,
            companyname: true,
            contactname: true,
            contactemail: true
          }
        },
        projects: {
          select: {
            id: true,
            name: true,
            status: true
          }
        },
        invoiceitems: true,
        payments: {
          select: {
            id: true,
            amount: true,
            status: true,
            paymentdate: true
          }
        }
      }
    })
  })

  // Transform the response
  const transformedInvoice = {
    ...transformFromDbFields(invoice),
    client: invoice.clients ? transformFromDbFields(invoice.clients) : null,
    project: invoice.projects ? transformFromDbFields(invoice.projects) : null,
    items: invoice.invoiceitems ? invoice.invoiceitems.map(item => transformFromDbFields(item)) : [],
    payments: invoice.payments ? invoice.payments.map(payment => ({
      ...transformFromDbFields(payment),
      paidAt: payment.paymentdate
    })) : []
  }

  return successResponse(transformedInvoice, 'Invoice updated successfully')
})

// DELETE /api/admin/invoices/[id] - Delete an invoice
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  // Temporarily bypass auth for development/testing
  // await requireAdmin(request)
  const { id } = await params

  // Check if invoice has payments
  const paymentsCount = await prisma.payments.count({
    where: { invoiceid: BigInt(id) }
  })

  if (paymentsCount > 0) {
    throw new ApiError('Cannot delete invoice with associated payments', 400)
  }

  // Delete invoice items first
  await prisma.invoiceitems.deleteMany({
    where: { invoiceid: BigInt(id) }
  })

  // Then delete the invoice
  await prisma.invoices.delete({
    where: { id: BigInt(id) }
  })

  return successResponse(null, 'Invoice deleted successfully')
})
