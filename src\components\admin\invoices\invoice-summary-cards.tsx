'use client'

import { motion } from 'framer-motion'
import {
  CurrencyDollarIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  CalendarDaysIcon
} from '@heroicons/react/24/outline'

interface SummaryData {
  totalOutstanding: number
  totalPaid: number
  overdueInvoices: number
  upcomingPayments: number
  averagePaymentTime: number
  totalRevenue: number
  totalInvoices: number
}

interface InvoiceSummaryCardsProps {
  data: SummaryData
  loading?: boolean
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

const formatCompactCurrency = (amount: number) => {
  if (amount >= 1000000) {
    return `$${(amount / 1000000).toFixed(1)}M`
  } else if (amount >= 1000) {
    return `$${(amount / 1000).toFixed(1)}K`
  }
  return formatCurrency(amount)
}

export default function InvoiceSummaryCards({ data, loading = false }: InvoiceSummaryCardsProps) {
  const cards = [
    {
      title: 'Total Outstanding',
      value: data.totalOutstanding,
      formattedValue: formatCompactCurrency(data.totalOutstanding),
      icon: CurrencyDollarIcon,
      color: 'bg-red-500',
      bgColor: 'bg-red-50',
      textColor: 'text-red-600',
      description: 'Unpaid invoices',
      trend: null
    },
    {
      title: 'Total Paid',
      value: data.totalPaid,
      formattedValue: formatCompactCurrency(data.totalPaid),
      icon: CurrencyDollarIcon,
      color: 'bg-green-500',
      bgColor: 'bg-green-50',
      textColor: 'text-green-600',
      description: 'Collected revenue',
      trend: null
    },
    {
      title: 'Overdue Invoices',
      value: data.overdueInvoices,
      formattedValue: data.overdueInvoices.toString(),
      icon: ExclamationTriangleIcon,
      color: 'bg-orange-500',
      bgColor: 'bg-orange-50',
      textColor: 'text-orange-600',
      description: 'Past due date',
      trend: null
    },
    {
      title: 'Upcoming Payments',
      value: data.upcomingPayments,
      formattedValue: data.upcomingPayments.toString(),
      icon: CalendarDaysIcon,
      color: 'bg-blue-500',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-600',
      description: 'Due in 7 days',
      trend: null
    },
    {
      title: 'Average Payment Time',
      value: data.averagePaymentTime,
      formattedValue: `${data.averagePaymentTime} days`,
      icon: ClockIcon,
      color: 'bg-purple-500',
      bgColor: 'bg-purple-50',
      textColor: 'text-purple-600',
      description: 'Time to payment',
      trend: data.averagePaymentTime <= 30 ? 'up' : 'down'
    },
    {
      title: 'Total Revenue',
      value: data.totalRevenue,
      formattedValue: formatCompactCurrency(data.totalRevenue),
      icon: TrendingUpIcon,
      color: 'bg-indigo-500',
      bgColor: 'bg-indigo-50',
      textColor: 'text-indigo-600',
      description: 'All time revenue',
      trend: 'up'
    }
  ]

  if (loading) {
    return (
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
        {[...Array(6)].map((_, index) => (
          <div key={index} className="bg-white overflow-hidden shadow rounded-lg animate-pulse">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-gray-200 rounded-md"></div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-6 bg-gray-200 rounded"></div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6">
      {cards.map((card, index) => {
        const Icon = card.icon
        const TrendIcon = card.trend === 'up' ? TrendingUpIcon : card.trend === 'down' ? TrendingDownIcon : null
        
        return (
          <motion.div
            key={card.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white overflow-hidden shadow rounded-lg hover:shadow-lg transition-shadow duration-200"
          >
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`w-8 h-8 ${card.color} rounded-md flex items-center justify-center`}>
                    <Icon className="h-5 w-5 text-white" />
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {card.title}
                    </dt>
                    <dd className="flex items-baseline">
                      <div className={`text-2xl font-semibold ${card.textColor}`}>
                        {card.formattedValue}
                      </div>
                      {TrendIcon && (
                        <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                          card.trend === 'up' ? 'text-green-600' : 'text-red-600'
                        }`}>
                          <TrendIcon className="self-center flex-shrink-0 h-4 w-4" />
                        </div>
                      )}
                    </dd>
                  </dl>
                </div>
              </div>
              <div className="mt-3">
                <div className="text-sm text-gray-500">
                  {card.description}
                </div>
                
                {/* Progress bar for certain metrics */}
                {(card.title === 'Total Outstanding' || card.title === 'Total Paid') && (
                  <div className="mt-2">
                    <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
                      <span>Collection Rate</span>
                      <span>
                        {data.totalRevenue > 0 
                          ? Math.round((data.totalPaid / data.totalRevenue) * 100)
                          : 0
                        }%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-1.5">
                      <div 
                        className={`h-1.5 rounded-full ${
                          card.title === 'Total Paid' ? 'bg-green-500' : 'bg-red-500'
                        }`}
                        style={{ 
                          width: data.totalRevenue > 0 
                            ? `${Math.min((card.value / data.totalRevenue) * 100, 100)}%`
                            : '0%'
                        }}
                      />
                    </div>
                  </div>
                )}

                {/* Status indicator for overdue invoices */}
                {card.title === 'Overdue Invoices' && (
                  <div className="mt-2">
                    <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      data.overdueInvoices === 0 
                        ? 'bg-green-100 text-green-800'
                        : data.overdueInvoices <= 5
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {data.overdueInvoices === 0 
                        ? 'All current'
                        : data.overdueInvoices <= 5
                        ? 'Manageable'
                        : 'Needs attention'
                      }
                    </div>
                  </div>
                )}

                {/* Payment time indicator */}
                {card.title === 'Average Payment Time' && (
                  <div className="mt-2">
                    <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      data.averagePaymentTime <= 15
                        ? 'bg-green-100 text-green-800'
                        : data.averagePaymentTime <= 30
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {data.averagePaymentTime <= 15
                        ? 'Excellent'
                        : data.averagePaymentTime <= 30
                        ? 'Good'
                        : 'Slow'
                      }
                    </div>
                  </div>
                )}
              </div>
            </div>
          </motion.div>
        )
      })}
    </div>
  )
}
