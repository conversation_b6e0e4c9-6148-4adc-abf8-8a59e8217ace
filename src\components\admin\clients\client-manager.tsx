'use client'

import React, { useState, useEffect, useCallback } from 'react'
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  ViewColumnsIcon,
  Squares2X2Icon,
  PlusIcon,
  ArrowDownTrayIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline'

import ClientTable from './client-table'
import ClientGrid from './client-grid'
import ClientSummaryCards from './client-summary-cards'
import ClientProfileModal from './client-profile-modal'
import ContractModal from './contract-modal'

interface Client {
  id: string
  companyName: string
  contactName: string
  contactEmail: string
  contactPhone?: string
  website?: string
  address?: string
  city?: string
  state?: string
  country?: string
  isActive: boolean
  createdAt: string
  _count: {
    projects: number
    contracts: number
    invoices: number
    orders: number
    testimonials: number
  }
  recentProjects?: Array<{
    id: string
    name: string
    status: string
    createdAt: string
  }>
  recentContracts?: Array<{
    id: string
    name: string
    status: string
    value: number
    createdAt: string
  }>
  recentInvoices?: Array<{
    id: string
    invoiceNumber: string
    totalAmount: number
    status: string
    createdAt: string
  }>
  recentOrders?: Array<{
    id: string
    title: string
    totalAmount: number
    status: string
    createdAt: string
  }>
}

interface ClientStats {
  totalClients: number
  activeClients: number
  inactiveClients: number
  newClientsThisMonth: number
  totalProjects: number
  activeProjects: number
  totalContracts: number
  activeContracts: number
  totalContractValue: number
  totalInvoices: number
  paidInvoices: number
  pendingInvoices: number
  overdueInvoices: number
  totalRevenue: number
  monthlyGrowth: number
  averageProjectValue: number
  clientRetentionRate: number
}

export default function ClientManager() {
  const [clients, setClients] = useState<Client[]>([])
  const [stats, setStats] = useState<ClientStats>({
    totalClients: 0,
    activeClients: 0,
    inactiveClients: 0,
    newClientsThisMonth: 0,
    totalProjects: 0,
    activeProjects: 0,
    totalContracts: 0,
    activeContracts: 0,
    totalContractValue: 0,
    totalInvoices: 0,
    paidInvoices: 0,
    pendingInvoices: 0,
    overdueInvoices: 0,
    totalRevenue: 0,
    monthlyGrowth: 0,
    averageProjectValue: 0,
    clientRetentionRate: 0
  })
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [sortBy, setSortBy] = useState('createdAt')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [viewMode, setViewMode] = useState<'table' | 'grid'>('table')

  // Modal states
  const [selectedClient, setSelectedClient] = useState<Client | null>(null)
  const [profileModalOpen, setProfileModalOpen] = useState(false)
  const [contractModalOpen, setContractModalOpen] = useState(false)
  const [contractModalMode, setContractModalMode] = useState<'create' | 'edit' | 'view'>('create')
  const [selectedContract, setSelectedContract] = useState(null)

  const fetchClients = useCallback(async () => {
    setLoading(true)
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '12',
        search: searchTerm,
        sortBy,
        sortOrder,
        ...(statusFilter !== 'all' && { filter: statusFilter })
      })

      const response = await fetch(`/api/admin/clients?${params}`)
      
      if (!response.ok) {
        const errorText = await response.text()
        console.error('API Error Response:', response.status, response.statusText, errorText)
        throw new Error(`Failed to fetch clients: ${response.status} ${response.statusText}`)
      }

      const result = await response.json()
      console.log('API Response:', result)
      
      if (result.success) {
        setClients(result.data || [])
        setTotalPages(Math.ceil((result.total || 0) / 12))
        
        // Calculate stats from the data
        const clientData = result.data || []
        const newStats: ClientStats = {
          totalClients: result.total || 0,
          activeClients: clientData.filter((c: Client) => c.isActive).length,
          inactiveClients: clientData.filter((c: Client) => !c.isActive).length,
          newClientsThisMonth: clientData.filter((c: Client) => {
            const createdDate = new Date(c.createdAt)
            const now = new Date()
            const monthAgo = new Date(now.getFullYear(), now.getMonth(), 1)
            return createdDate >= monthAgo
          }).length,
          totalProjects: clientData.reduce((sum: number, c: Client) => sum + c._count.projects, 0),
          activeProjects: clientData.reduce((sum: number, c: Client) => sum + c._count.projects, 0), // Simplified
          totalContracts: clientData.reduce((sum: number, c: Client) => sum + c._count.contracts, 0),
          activeContracts: clientData.reduce((sum: number, c: Client) => sum + c._count.contracts, 0), // Simplified
          totalContractValue: clientData.reduce((sum: number, c: Client) => {
            return sum + (c.recentContracts?.reduce((contractSum, contract) => contractSum + contract.value, 0) || 0)
          }, 0),
          totalInvoices: clientData.reduce((sum: number, c: Client) => sum + c._count.invoices, 0),
          paidInvoices: clientData.reduce((sum: number, c: Client) => {
            return sum + (c.recentInvoices?.filter(inv => inv.status === 'Paid').length || 0)
          }, 0),
          pendingInvoices: clientData.reduce((sum: number, c: Client) => {
            return sum + (c.recentInvoices?.filter(inv => inv.status === 'Pending' || inv.status === 'Sent').length || 0)
          }, 0),
          overdueInvoices: clientData.reduce((sum: number, c: Client) => {
            return sum + (c.recentInvoices?.filter(inv => inv.status === 'Overdue').length || 0)
          }, 0),
          totalRevenue: clientData.reduce((sum: number, c: Client) => {
            return sum + (c.recentInvoices?.reduce((invSum, inv) => invSum + inv.totalAmount, 0) || 0)
          }, 0),
          monthlyGrowth: Math.random() * 10 - 5, // Placeholder
          averageProjectValue: clientData.length > 0 ? 
            clientData.reduce((sum: number, c: Client) => {
              return sum + (c.recentInvoices?.reduce((invSum, inv) => invSum + inv.totalAmount, 0) || 0)
            }, 0) / clientData.length : 0,
          clientRetentionRate: 85 + Math.random() * 10 // Placeholder
        }
        setStats(newStats)
      } else {
        console.error('API returned unsuccessful response:', result)
        throw new Error(result.message || 'Failed to fetch clients')
      }
    } catch (error) {
      console.error('Error fetching clients:', error)
      // Set empty data on error to prevent UI crashes
      setClients([])
    } finally {
      setLoading(false)
    }
  }, [currentPage, searchTerm, statusFilter, sortBy, sortOrder])

  useEffect(() => {
    fetchClients()
  }, [fetchClients])

  const handleSearch = (term: string) => {
    setSearchTerm(term)
    setCurrentPage(1)
  }

  const handleFilterChange = (filter: string) => {
    setStatusFilter(filter)
    setCurrentPage(1)
  }

  const handleSort = (field: string, order: 'asc' | 'desc') => {
    setSortBy(field)
    setSortOrder(order)
    setCurrentPage(1)
  }

  const handleViewClient = (client: Client) => {
    setSelectedClient(client)
    setProfileModalOpen(true)
  }

  const handleEditClient = (client: Client) => {
    // TODO: Implement edit client modal
    console.log('Edit client:', client)
  }

  const handleDeleteClient = async (clientId: string) => {
    if (!confirm('Are you sure you want to delete this client?')) {
      return
    }

    try {
      const response = await fetch(`/api/admin/clients/${clientId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        await fetchClients()
      } else {
        console.error('Failed to delete client')
      }
    } catch (error) {
      console.error('Error deleting client:', error)
    }
  }

  const handleViewProjects = (client: Client) => {
    // TODO: Navigate to projects page with client filter
    console.log('View projects for client:', client)
  }

  const handleViewContracts = (client: Client) => {
    setSelectedClient(client)
    setContractModalMode('create')
    setContractModalOpen(true)
  }

  const handleViewInvoices = (client: Client) => {
    // TODO: Navigate to invoices page with client filter
    console.log('View invoices for client:', client)
  }

  const handleViewPayments = (client: Client) => {
    // TODO: Navigate to payments page with client filter
    console.log('View payments for client:', client)
  }

  const handleSaveContract = async (contractData: any) => {
    try {
      const response = await fetch('/api/admin/contracts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(contractData)
      })

      if (response.ok) {
        setContractModalOpen(false)
        await fetchClients() // Refresh data
      } else {
        console.error('Failed to save contract')
      }
    } catch (error) {
      console.error('Error saving contract:', error)
    }
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <ClientSummaryCards stats={stats} loading={loading} />

      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Clients</h1>
          <p className="mt-2 text-sm text-gray-700">
            Manage your clients and their associated projects, contracts, and invoices.
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <button
            onClick={() => {/* TODO: Implement export */}}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
            Export
          </button>
          <button
            onClick={() => {/* TODO: Implement add client */}}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Client
          </button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 sm:space-x-4">
        <div className="flex-1 max-w-lg">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Search clients..."
            />
          </div>
        </div>

        <div className="flex items-center space-x-4">
          {/* Status Filter */}
          <div className="flex items-center space-x-2">
            <FunnelIcon className="h-5 w-5 text-gray-400" />
            <select
              value={statusFilter}
              onChange={(e) => handleFilterChange(e.target.value)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>

          {/* View Toggle */}
          <div className="flex items-center border border-gray-300 rounded-md">
            <button
              onClick={() => setViewMode('table')}
              className={`p-2 ${
                viewMode === 'table'
                  ? 'bg-blue-100 text-blue-600'
                  : 'text-gray-400 hover:text-gray-600'
              }`}
            >
              <ViewColumnsIcon className="h-5 w-5" />
            </button>
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 ${
                viewMode === 'grid'
                  ? 'bg-blue-100 text-blue-600'
                  : 'text-gray-400 hover:text-gray-600'
              }`}
            >
              <Squares2X2Icon className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Client List */}
      {viewMode === 'table' ? (
        <div className="bg-white shadow-sm rounded-lg border border-gray-200">
          <ClientTable
            clients={clients}
            loading={loading}
            onView={handleViewClient}
            onEdit={handleEditClient}
            onDelete={handleDeleteClient}
            onViewProjects={handleViewProjects}
            onViewContracts={handleViewContracts}
            onViewInvoices={handleViewInvoices}
            onViewPayments={handleViewPayments}
          />
        </div>
      ) : (
        <ClientGrid
          clients={clients}
          loading={loading}
          onView={handleViewClient}
          onEdit={handleEditClient}
          onDelete={handleDeleteClient}
          onViewProjects={handleViewProjects}
          onViewContracts={handleViewContracts}
          onViewInvoices={handleViewInvoices}
          onViewPayments={handleViewPayments}
        />
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing page <span className="font-medium">{currentPage}</span> of{' '}
                <span className="font-medium">{totalPages}</span>
              </p>
            </div>
            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Modals */}
      <ClientProfileModal
        client={selectedClient}
        isOpen={profileModalOpen}
        onClose={() => {
          setProfileModalOpen(false)
          setSelectedClient(null)
        }}
        onEdit={handleEditClient}
      />

      <ContractModal
        isOpen={contractModalOpen}
        onClose={() => {
          setContractModalOpen(false)
          setSelectedClient(null)
          setSelectedContract(null)
        }}
        onSave={handleSaveContract}
        client={selectedClient}
        contract={selectedContract}
        mode={contractModalMode}
      />
    </div>
  )
}
