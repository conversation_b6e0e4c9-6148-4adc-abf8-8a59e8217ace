'use client'

import React, { useState, useEffect } from 'react'
import {
  XMarkIcon,
  BuildingOfficeIcon,
  UserIcon,
  EnvelopeIcon,
  PhoneIcon,
  GlobeAltIcon,
  MapPinIcon,
  FolderIcon,
  DocumentTextIcon,
  CreditCardIcon,
  StarIcon,
  CalendarIcon,
  PencilIcon,
  PlusIcon
} from '@heroicons/react/24/outline'
import ProjectModal from '../projects/project-modal'
import ContractModal from './contract-modal'
import InvoiceModal from '../invoices/invoice-modal'

interface Client {
  id: string
  companyName: string
  contactName: string
  contactPosition?: string
  contactEmail: string
  contactPhone?: string
  contactFax?: string
  website?: string
  address?: string
  city?: string
  state?: string
  zipCode?: string
  country?: string
  logoUrl?: string
  notes?: string
  isActive: boolean
  createdAt: string
  updatedAt?: string
  _count: {
    projects: number
    contracts: number
    invoices: number
    orders: number
    testimonials: number
  }
  projects?: Array<{
    id: string
    name: string
    description?: string
    status: string
    startDate?: string
    completionDate?: string
    estimatedCost: number
    createdAt: string
    _count?: {
      invoices: number
      contracts: number
    }
  }>
  contracts?: Array<{
    id: string
    name: string
    status: string
    value: number
    serviceType?: string
    language?: string
    description?: string
    currency?: string
    billingType?: string
    nextBillDate?: string
    signMethod?: string
    signedDate?: string
    executedDate?: string
    expiryDate?: string
    createdAt: string
  }>
  invoices?: Array<{
    id: string
    invoiceNumber: string
    totalAmount: number
    status: string
    dueDate?: string
    paidAt?: string
    createdAt: string
    payments?: Array<{
      id: string
      amount: number
      paymentDate: string
      status: string
    }>
  }>
  orders?: Array<{
    id: string
    title: string
    description?: string
    totalAmount: number
    status: string
    orderDate?: string
    createdAt: string
  }>
  testimonials?: Array<{
    id: string
    clientName: string
    clientTitle?: string
    clientCompany?: string
    content: string
    rating?: number
    isFeatured: boolean
    createdAt: string
  }>
}

interface ClientProfileModalProps {
  client: Client | null
  isOpen: boolean
  onClose: () => void
  onEdit: (client: Client) => void
  initialTab?: string
}

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'active':
    case 'completed':
    case 'paid':
      return 'bg-green-100 text-green-800'
    case 'inactive':
    case 'cancelled':
      return 'bg-red-100 text-red-800'
    case 'pending':
    case 'draft':
      return 'bg-yellow-100 text-yellow-800'
    case 'in_progress':
    case 'sent':
      return 'bg-blue-100 text-blue-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

export default function ClientProfileModal({
  client,
  isOpen,
  onClose,
  onEdit,
  initialTab = 'overview'
}: ClientProfileModalProps) {
  const [activeTab, setActiveTab] = useState(initialTab)
  const [clientDetails, setClientDetails] = useState<Client | null>(null)
  const [loading, setLoading] = useState(false)

  // Modal states
  const [projectModalOpen, setProjectModalOpen] = useState(false)
  const [contractModalOpen, setContractModalOpen] = useState(false)
  const [invoiceModalOpen, setInvoiceModalOpen] = useState(false)

  useEffect(() => {
    if (client && isOpen) {
      // Set basic client data immediately as fallback
      setClientDetails(client)
      // Then try to fetch detailed data
      fetchClientDetails(client.id)
      setActiveTab(initialTab)
    }
  }, [client, isOpen, initialTab])

  const fetchClientDetails = async (clientId: string) => {
    setLoading(true)
    try {
      const response = await fetch(`/api/admin/clients/${clientId}`)

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setClientDetails(result.data)
        }
      }
    } catch (error) {
      console.error('Error fetching client details:', error)
      // Continue with basic client data if detailed fetch fails
    } finally {
      setLoading(false)
    }
  }

  // Handler functions for creating new items
  const handleCreateProject = async (projectData: any) => {
    try {
      const response = await fetch('/api/admin/projects', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...projectData,
          clientid: safeDisplayClient.id
        })
      })

      if (response.ok) {
        setProjectModalOpen(false)
        await fetchClientDetails(safeDisplayClient.id) // Refresh data
      } else {
        throw new Error('Failed to create project')
      }
    } catch (error) {
      console.error('Error creating project:', error)
      alert('Failed to create project')
    }
  }

  const handleCreateContract = async (contractData: any) => {
    try {
      const response = await fetch('/api/admin/contracts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...contractData,
          clientId: safeDisplayClient.id
        })
      })

      if (response.ok) {
        setContractModalOpen(false)
        await fetchClientDetails(safeDisplayClient.id) // Refresh data
      } else {
        throw new Error('Failed to create contract')
      }
    } catch (error) {
      console.error('Error creating contract:', error)
      alert('Failed to create contract')
    }
  }

  const handleCreateInvoice = async () => {
    try {
      setInvoiceModalOpen(false)
      await fetchClientDetails(safeDisplayClient.id) // Refresh data
    } catch (error) {
      console.error('Error creating invoice:', error)
      alert('Failed to create invoice')
    }
  }

  if (!isOpen || !client) return null

  const displayClient = clientDetails || client

  // Ensure _count exists with default values
  const safeDisplayClient = {
    ...displayClient,
    _count: displayClient._count || {
      projects: 0,
      contracts: 0,
      invoices: 0,
      orders: 0,
      testimonials: 0
    }
  }

  const tabs = [
    { id: 'overview', name: 'Overview', icon: UserIcon },
    { id: 'projects', name: 'Projects', icon: FolderIcon, count: safeDisplayClient._count.projects },
    { id: 'contracts', name: 'Contracts', icon: DocumentTextIcon, count: safeDisplayClient._count.contracts },
    { id: 'invoices', name: 'Invoices', icon: CreditCardIcon, count: safeDisplayClient._count.invoices },
    { id: 'testimonials', name: 'Reviews', icon: StarIcon, count: safeDisplayClient._count.testimonials }
  ]

  return (
    <div className="fixed inset-0 z-[70] overflow-y-auto" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={onClose}></div>

        <span className="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>

        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full">
          {/* Header */}
          <div className="bg-white px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">
                  <div className="h-12 w-12 rounded-full bg-gray-100 flex items-center justify-center">
                    <BuildingOfficeIcon className="h-6 w-6 text-gray-500" />
                  </div>
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-900">
                    {safeDisplayClient.companyName}
                  </h3>
                  <p className="text-sm text-gray-500">{safeDisplayClient.contactName}</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => onEdit(safeDisplayClient)}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <PencilIcon className="h-4 w-4 mr-2" />
                  Edit
                </button>
                <button
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>
            </div>
          </div>

          {/* Tabs */}
          <div className="border-b border-gray-200">
            <nav className="-mb-px flex space-x-8 px-6">
              {tabs.map((tab) => {
                const Icon = tab.icon
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    <span>{tab.name}</span>
                    {tab.count !== undefined && (
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        activeTab === tab.id ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                      }`}>
                        {tab.count}
                      </span>
                    )}
                  </button>
                )
              })}
            </nav>
          </div>

          {/* Content */}
          <div className="bg-white px-6 py-6 max-h-96 overflow-y-auto">
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : (
              <>
                {activeTab === 'overview' && (
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Contact Information */}
                    <div>
                      <h4 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h4>
                      <div className="space-y-3">
                        <div className="flex items-center">
                          <UserIcon className="h-5 w-5 text-gray-400 mr-3" />
                          <div>
                            <div className="text-sm font-medium text-gray-900">{safeDisplayClient.contactName}</div>
                            {safeDisplayClient.contactPosition && (
                              <div className="text-sm text-gray-500">{safeDisplayClient.contactPosition}</div>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center">
                          <EnvelopeIcon className="h-5 w-5 text-gray-400 mr-3" />
                          <div className="text-sm text-gray-900">{safeDisplayClient.contactEmail}</div>
                        </div>
                        {safeDisplayClient.contactPhone && (
                          <div className="flex items-center">
                            <PhoneIcon className="h-5 w-5 text-gray-400 mr-3" />
                            <div className="text-sm text-gray-900">{safeDisplayClient.contactPhone}</div>
                          </div>
                        )}
                        {safeDisplayClient.website && (
                          <div className="flex items-center">
                            <GlobeAltIcon className="h-5 w-5 text-gray-400 mr-3" />
                            <div className="text-sm text-gray-900">{safeDisplayClient.website}</div>
                          </div>
                        )}
                        {(safeDisplayClient.address || safeDisplayClient.city || safeDisplayClient.state || safeDisplayClient.country) && (
                          <div className="flex items-start">
                            <MapPinIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                            <div className="text-sm text-gray-900">
                              {[safeDisplayClient.address, safeDisplayClient.city, safeDisplayClient.state, safeDisplayClient.zipCode, safeDisplayClient.country]
                                .filter(Boolean)
                                .join(', ')}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Company Information */}
                    <div>
                      <h4 className="text-lg font-medium text-gray-900 mb-4">Company Information</h4>
                      <div className="space-y-3">
                        <div>
                          <div className="text-sm font-medium text-gray-500">Status</div>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            safeDisplayClient.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                          }`}>
                            {safeDisplayClient.isActive ? 'Active' : 'Inactive'}
                          </span>
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-500">Created</div>
                          <div className="text-sm text-gray-900">{formatDate(safeDisplayClient.createdAt)}</div>
                        </div>
                        {safeDisplayClient.updatedAt && (
                          <div>
                            <div className="text-sm font-medium text-gray-500">Last Updated</div>
                            <div className="text-sm text-gray-900">{formatDate(safeDisplayClient.updatedAt)}</div>
                          </div>
                        )}
                        {safeDisplayClient.notes && (
                          <div>
                            <div className="text-sm font-medium text-gray-500">Notes</div>
                            <div className="text-sm text-gray-900">{safeDisplayClient.notes}</div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'projects' && (
                  <div>
                    <div className="flex justify-between items-center mb-4">
                      <h4 className="text-lg font-medium text-gray-900">Projects ({safeDisplayClient._count.projects})</h4>
                      <button
                        onClick={() => setProjectModalOpen(true)}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        <PlusIcon className="h-4 w-4 mr-1" />
                        Add Project
                      </button>
                    </div>
                    {safeDisplayClient.projects && safeDisplayClient.projects.length > 0 ? (
                      <div className="space-y-4">
                        {safeDisplayClient.projects.map((project) => (
                          <div key={project.id} className="border border-gray-200 rounded-lg p-4">
                            <div className="flex justify-between items-start mb-2">
                              <h5 className="font-medium text-gray-900">{project.name}</h5>
                              <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(project.status)}`}>
                                {project.status}
                              </span>
                            </div>
                            {project.description && (
                              <p className="text-sm text-gray-600 mb-2">{project.description}</p>
                            )}
                            <div className="grid grid-cols-2 gap-4 text-sm">
                              <div>
                                <span className="font-medium text-gray-500">Estimated Cost:</span> {formatCurrency(project.estimatedCost)}
                              </div>
                              <div>
                                <span className="font-medium text-gray-500">Created:</span> {formatDate(project.createdAt)}
                              </div>
                              {project.startDate && (
                                <div>
                                  <span className="font-medium text-gray-500">Start Date:</span> {formatDate(project.startDate)}
                                </div>
                              )}
                              {project.completionDate && (
                                <div>
                                  <span className="font-medium text-gray-500">Completion:</span> {formatDate(project.completionDate)}
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <FolderIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No projects</h3>
                        <p className="mt-1 text-sm text-gray-500">This client doesn't have any projects yet.</p>
                      </div>
                    )}
                  </div>
                )}

                {activeTab === 'contracts' && (
                  <div>
                    <div className="flex justify-between items-center mb-4">
                      <h4 className="text-lg font-medium text-gray-900">Contracts ({safeDisplayClient._count.contracts})</h4>
                      <button
                        onClick={() => setContractModalOpen(true)}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        <PlusIcon className="h-4 w-4 mr-1" />
                        Add Contract
                      </button>
                    </div>
                    {safeDisplayClient.contracts && safeDisplayClient.contracts.length > 0 ? (
                      <div className="space-y-4">
                        {safeDisplayClient.contracts.map((contract) => (
                          <div key={contract.id} className="border border-gray-200 rounded-lg p-4">
                            <div className="flex justify-between items-start mb-2">
                              <h5 className="font-medium text-gray-900">{contract.name}</h5>
                              <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(contract.status)}`}>
                                {contract.status}
                              </span>
                            </div>
                            {contract.description && (
                              <p className="text-sm text-gray-600 mb-2">{contract.description}</p>
                            )}
                            <div className="grid grid-cols-2 gap-4 text-sm">
                              <div>
                                <span className="font-medium text-gray-500">Value:</span> {formatCurrency(contract.value)}
                              </div>
                              <div>
                                <span className="font-medium text-gray-500">Created:</span> {formatDate(contract.createdAt)}
                              </div>
                              {contract.serviceType && (
                                <div>
                                  <span className="font-medium text-gray-500">Service Type:</span> {contract.serviceType}
                                </div>
                              )}
                              {contract.billingType && (
                                <div>
                                  <span className="font-medium text-gray-500">Billing Type:</span> {contract.billingType}
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No contracts</h3>
                        <p className="mt-1 text-sm text-gray-500">This client doesn't have any contracts yet.</p>
                      </div>
                    )}
                  </div>
                )}

                {activeTab === 'invoices' && (
                  <div>
                    <div className="flex justify-between items-center mb-4">
                      <h4 className="text-lg font-medium text-gray-900">Invoices ({safeDisplayClient._count.invoices})</h4>
                      <button
                        onClick={() => setInvoiceModalOpen(true)}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        <PlusIcon className="h-4 w-4 mr-1" />
                        Add Invoice
                      </button>
                    </div>
                    {safeDisplayClient.invoices && safeDisplayClient.invoices.length > 0 ? (
                      <div className="space-y-4">
                        {safeDisplayClient.invoices.map((invoice) => (
                          <div key={invoice.id} className="border border-gray-200 rounded-lg p-4">
                            <div className="flex justify-between items-start mb-2">
                              <h5 className="font-medium text-gray-900">{invoice.invoiceNumber}</h5>
                              <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(invoice.status)}`}>
                                {invoice.status}
                              </span>
                            </div>
                            <div className="grid grid-cols-2 gap-4 text-sm">
                              <div>
                                <span className="font-medium text-gray-500">Amount:</span> {formatCurrency(invoice.totalAmount)}
                              </div>
                              <div>
                                <span className="font-medium text-gray-500">Created:</span> {formatDate(invoice.createdAt)}
                              </div>
                              {invoice.dueDate && (
                                <div>
                                  <span className="font-medium text-gray-500">Due Date:</span> {formatDate(invoice.dueDate)}
                                </div>
                              )}
                              {invoice.paidAt && (
                                <div>
                                  <span className="font-medium text-gray-500">Paid At:</span> {formatDate(invoice.paidAt)}
                                </div>
                              )}
                            </div>
                            {invoice.payments && invoice.payments.length > 0 && (
                              <div className="mt-3">
                                <div className="text-sm font-medium text-gray-500 mb-2">Payments:</div>
                                <div className="space-y-1">
                                  {invoice.payments.map((payment) => (
                                    <div key={payment.id} className="flex justify-between text-sm">
                                      <span>{formatDate(payment.paymentDate)}</span>
                                      <span>{formatCurrency(payment.amount)}</span>
                                      <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(payment.status)}`}>
                                        {payment.status}
                                      </span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <CreditCardIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No invoices</h3>
                        <p className="mt-1 text-sm text-gray-500">This client doesn't have any invoices yet.</p>
                      </div>
                    )}
                  </div>
                )}

                {activeTab === 'testimonials' && (
                  <div>
                    <h4 className="text-lg font-medium text-gray-900 mb-4">Reviews ({safeDisplayClient._count.testimonials})</h4>
                    {safeDisplayClient.testimonials && safeDisplayClient.testimonials.length > 0 ? (
                      <div className="space-y-4">
                        {safeDisplayClient.testimonials.map((testimonial) => (
                          <div key={testimonial.id} className="border border-gray-200 rounded-lg p-4">
                            <div className="flex justify-between items-start mb-2">
                              <div>
                                <h5 className="font-medium text-gray-900">{testimonial.clientName}</h5>
                                {testimonial.clientTitle && testimonial.clientCompany && (
                                  <p className="text-sm text-gray-500">{testimonial.clientTitle} at {testimonial.clientCompany}</p>
                                )}
                              </div>
                              <div className="flex items-center">
                                {testimonial.rating && (
                                  <div className="flex items-center">
                                    {[...Array(5)].map((_, i) => (
                                      <StarIcon
                                        key={i}
                                        className={`h-4 w-4 ${
                                          i < testimonial.rating! ? 'text-yellow-400 fill-current' : 'text-gray-300'
                                        }`}
                                      />
                                    ))}
                                  </div>
                                )}
                                {testimonial.isFeatured && (
                                  <span className="ml-2 px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800">
                                    Featured
                                  </span>
                                )}
                              </div>
                            </div>
                            <p className="text-sm text-gray-600 mb-2">{testimonial.content}</p>
                            <div className="text-xs text-gray-500">
                              {formatDate(testimonial.createdAt)}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <StarIcon className="mx-auto h-12 w-12 text-gray-400" />
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No reviews</h3>
                        <p className="mt-1 text-sm text-gray-500">This client doesn't have any reviews yet.</p>
                      </div>
                    )}
                  </div>
                )}
              </>
            )}
          </div>
        </div>
      </div>

      {/* Modals */}
      <ProjectModal
        isOpen={projectModalOpen}
        onClose={() => setProjectModalOpen(false)}
        onSubmit={handleCreateProject}
        title="Create New Project"
        preSelectedClientId={safeDisplayClient.id}
      />

      <ContractModal
        isOpen={contractModalOpen}
        onClose={() => setContractModalOpen(false)}
        onSave={handleCreateContract}
        client={safeDisplayClient}
        mode="create"
      />

      <InvoiceModal
        isOpen={invoiceModalOpen}
        onClose={() => setInvoiceModalOpen(false)}
        onSuccess={handleCreateInvoice}
        title="Create New Invoice"
        preSelectedClientId={safeDisplayClient.id}
      />
    </div>
  )
}
