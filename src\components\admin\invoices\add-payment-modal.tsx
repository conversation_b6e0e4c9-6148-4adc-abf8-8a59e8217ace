'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  XMarkIcon, 
  CreditCardIcon, 
  BanknotesIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'

interface Invoice {
  id: string
  invoiceNumber: string
  totalAmount: number
  status: string
  client: {
    companyName: string
    contactName: string
  }
  payments?: Array<{
    id: string
    amount: number
    paymentDate: string
    paymentMethod: string
    status: string
  }>
}

interface AddPaymentModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  invoice: Invoice | null
}

const PAYMENT_METHODS = [
  { value: 'BANK_TRANSFER', label: 'Bank Transfer', icon: BanknotesIcon },
  { value: 'CREDIT_CARD', label: 'Credit Card', icon: CreditCardIcon },
  { value: 'CASH', label: 'Cash', icon: BanknotesIcon },
  { value: 'PAYPAL', label: 'PayPal', icon: CreditCardIcon },
  { value: 'CHECK', label: 'Check', icon: BanknotesIcon },
  { value: 'OTHER', label: 'Other', icon: BanknotesIcon },
]

export default function AddPaymentModal({
  isOpen,
  onClose,
  onSuccess,
  invoice
}: AddPaymentModalProps) {
  const [formData, setFormData] = useState({
    amount: '',
    paymentDate: new Date().toISOString().split('T')[0],
    paymentMethod: 'BANK_TRANSFER',
    transactionId: '',
    notes: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  // Calculate payment summary
  const totalPaid = invoice?.payments?.reduce((sum, payment) => 
    payment.status === 'COMPLETED' ? sum + payment.amount : sum, 0) || 0
  const remainingBalance = (invoice?.totalAmount || 0) - totalPaid
  const paymentAmount = parseFloat(formData.amount) || 0
  const newBalance = remainingBalance - paymentAmount

  useEffect(() => {
    if (isOpen && invoice) {
      setFormData({
        amount: '',
        paymentDate: new Date().toISOString().split('T')[0],
        paymentMethod: 'BANK_TRANSFER',
        transactionId: '',
        notes: ''
      })
      setError('')
    }
  }, [isOpen, invoice])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!invoice) return

    setLoading(true)
    setError('')

    try {
      // Validate payment amount
      if (paymentAmount <= 0) {
        throw new Error('Payment amount must be greater than 0')
      }

      if (paymentAmount > remainingBalance) {
        throw new Error('Payment amount cannot exceed remaining balance')
      }

      const response = await fetch('/api/admin/payments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          invoiceId: invoice.id,
          amount: paymentAmount,
          paymentDate: new Date(formData.paymentDate),
          paymentMethod: formData.paymentMethod,
          transactionId: formData.transactionId || undefined,
          notes: formData.notes || undefined
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to create payment')
      }

      onSuccess()
      onClose()
    } catch (error) {
      console.error('Error creating payment:', error)
      setError(error instanceof Error ? error.message : 'Failed to create payment')
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  if (!invoice) return null

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-screen items-center justify-center p-4">
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black bg-opacity-50"
              onClick={onClose}
            />

            {/* Modal */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden"
            >
              {/* Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Add Payment</h3>
                  <p className="text-sm text-gray-500">
                    Invoice {invoice.invoiceNumber} - {invoice.client.companyName}
                  </p>
                </div>
                <button
                  onClick={onClose}
                  className="text-gray-400 hover:text-gray-500 transition-colors"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>

              {/* Content */}
              <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Payment Form */}
                  <div>
                    <form onSubmit={handleSubmit} className="space-y-4">
                      {error && (
                        <div className="bg-red-50 border border-red-200 rounded-md p-3 flex items-center space-x-2">
                          <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                          <span className="text-sm text-red-700">{error}</span>
                        </div>
                      )}

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Payment Amount *
                        </label>
                        <div className="relative">
                          <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500">$</span>
                          <input
                            type="number"
                            step="0.01"
                            min="0.01"
                            max={remainingBalance}
                            required
                            value={formData.amount}
                            onChange={(e) => setFormData(prev => ({ ...prev, amount: e.target.value }))}
                            className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                            placeholder="0.00"
                          />
                        </div>
                        {paymentAmount > remainingBalance && (
                          <p className="text-sm text-red-600 mt-1">
                            Amount exceeds remaining balance
                          </p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Payment Date *
                        </label>
                        <input
                          type="date"
                          required
                          value={formData.paymentDate}
                          onChange={(e) => setFormData(prev => ({ ...prev, paymentDate: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Payment Method *
                        </label>
                        <select
                          required
                          value={formData.paymentMethod}
                          onChange={(e) => setFormData(prev => ({ ...prev, paymentMethod: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                        >
                          {PAYMENT_METHODS.map(method => (
                            <option key={method.value} value={method.value}>
                              {method.label}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Transaction ID / Reference
                        </label>
                        <input
                          type="text"
                          value={formData.transactionId}
                          onChange={(e) => setFormData(prev => ({ ...prev, transactionId: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Optional reference number"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Notes
                        </label>
                        <textarea
                          rows={3}
                          value={formData.notes}
                          onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="Optional internal notes"
                        />
                      </div>

                      <div className="flex space-x-3 pt-4">
                        <button
                          type="submit"
                          disabled={loading || paymentAmount <= 0 || paymentAmount > remainingBalance}
                          className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                        >
                          {loading ? 'Processing...' : 'Save Payment'}
                        </button>
                        <button
                          type="button"
                          onClick={onClose}
                          className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
                        >
                          Cancel
                        </button>
                      </div>
                    </form>
                  </div>

                  {/* Invoice Summary & Payment History */}
                  <div className="space-y-6">
                    {/* Invoice Summary */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-3">Invoice Summary</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Total Amount:</span>
                          <span className="font-medium">{formatCurrency(invoice.totalAmount)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Paid So Far:</span>
                          <span className="font-medium text-green-600">{formatCurrency(totalPaid)}</span>
                        </div>
                        <div className="flex justify-between border-t pt-2">
                          <span className="text-gray-600">Balance Due:</span>
                          <span className="font-medium text-red-600">{formatCurrency(remainingBalance)}</span>
                        </div>
                        {paymentAmount > 0 && (
                          <div className="flex justify-between border-t pt-2">
                            <span className="text-gray-600">After Payment:</span>
                            <span className={`font-medium ${newBalance <= 0 ? 'text-green-600' : 'text-orange-600'}`}>
                              {formatCurrency(Math.max(0, newBalance))}
                            </span>
                          </div>
                        )}
                      </div>
                      
                      {newBalance <= 0 && paymentAmount > 0 && (
                        <div className="mt-3 flex items-center space-x-2 text-green-600">
                          <CheckCircleIcon className="h-4 w-4" />
                          <span className="text-sm font-medium">Invoice will be fully paid</span>
                        </div>
                      )}
                    </div>

                    {/* Payment History */}
                    {invoice.payments && invoice.payments.length > 0 && (
                      <div>
                        <h4 className="font-medium text-gray-900 mb-3">Payment History</h4>
                        <div className="space-y-2">
                          {invoice.payments.map((payment) => (
                            <div key={payment.id} className="bg-white border rounded-lg p-3">
                              <div className="flex justify-between items-start">
                                <div>
                                  <div className="font-medium text-sm">{formatCurrency(payment.amount)}</div>
                                  <div className="text-xs text-gray-500">
                                    {new Date(payment.paymentDate).toLocaleDateString()} • {payment.paymentMethod}
                                  </div>
                                </div>
                                <span className={`px-2 py-1 text-xs rounded-full ${
                                  payment.status === 'COMPLETED' 
                                    ? 'bg-green-100 text-green-800' 
                                    : 'bg-yellow-100 text-yellow-800'
                                }`}>
                                  {payment.status}
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      )}
    </AnimatePresence>
  )
}
